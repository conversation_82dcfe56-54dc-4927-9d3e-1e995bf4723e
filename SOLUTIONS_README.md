# Docker配置管理解决方案

## 问题描述

原始项目中，每个微服务都有独立的配置文件（`projects/*/config.yaml`），当需要修改数据库配置时，需要打开多个文件进行修改，非常不便。

## 解决方案概览

我们提供了两个独立的解决方案，每个方案都在独立的目录中，可以单独运行：

### 方案1：环境变量配置方案 📁 `env-config-solution/`

**核心思想**：通过环境变量统一管理所有服务的数据库配置

**优势**：
- ✅ 统一管理：所有数据库配置集中在一个文件中
- ✅ DSN支持：支持DSN格式连接字符串，更加灵活
- ✅ 一次修改，全局生效：修改一个配置文件，所有服务自动生效
- ✅ 多环境支持：轻松切换开发、测试、生产环境

**适用场景**：
- 数据库配置相对统一的项目
- 希望集中管理配置的团队
- 需要快速切换环境的场景

### 方案2：多配置文件方案 📁 `multi-config-solution/`

**核心思想**：为每个服务创建多个环境配置文件，通过环境变量动态选择

**优势**：
- ✅ 服务独立配置：每个服务都有独立的环境配置文件
- ✅ 配置结构清晰：配置文件按服务和环境分类
- ✅ 个性化配置：支持服务级别的个性化配置
- ✅ 无需代码修改：不需要修改服务代码

**适用场景**：
- 服务配置差异较大的项目
- 需要服务级别个性化配置的场景
- 配置文件结构复杂的项目

## 目录结构

```
local-dev/
├── env-config-solution/          # 方案1：环境变量配置方案
│   ├── .env                      # 开发环境配置
│   ├── .env.prod                 # 生产环境配置
│   ├── docker-compose.yml        # 修改后的Docker Compose
│   ├── start.sh                  # 启动脚本
│   ├── projects/                 # 服务配置文件
│   └── README.md                 # 详细说明
│
├── multi-config-solution/        # 方案2：多配置文件方案
│   ├── docker-compose.yml        # 修改后的Docker Compose
│   ├── start.sh                  # 启动脚本
│   ├── setup-configs.sh          # 配置文件创建脚本
│   ├── projects/                 # 服务配置文件
│   │   ├── iam/
│   │   │   ├── config-dev.yaml   # 开发环境配置
│   │   │   ├── config-prod.yaml  # 生产环境配置
│   │   │   └── config-test.yaml  # 测试环境配置
│   │   └── ...
│   └── README.md                 # 详细说明
│
├── docker-compose.yml            # 原始文件（已恢复）
├── projects/                     # 原始配置文件（已恢复）
└── SOLUTIONS_README.md           # 本文档
```

## 快速开始

### 选择方案1（环境变量配置）

```bash
cd env-config-solution
./start.sh          # 启动开发环境
./start.sh prod      # 启动生产环境
```

### 选择方案2（多配置文件）

```bash
cd multi-config-solution
./setup-configs.sh  # 首次使用：创建配置文件
./start.sh          # 启动开发环境
./start.sh prod      # 启动生产环境
```

## 方案对比

| 特性 | 方案1（环境变量） | 方案2（多配置文件） |
|------|------------------|-------------------|
| **配置集中度** | 高（一个文件管理所有） | 低（每服务独立文件） |
| **修改便利性** | 极高（一处修改全局生效） | 中等（需修改多个文件） |
| **服务个性化** | 需要代码支持环境变量 | 天然支持 |
| **配置复杂度** | 简单 | 中等 |
| **维护成本** | 低 | 中等 |
| **灵活性** | 中等 | 高 |
| **学习成本** | 低 | 低 |
| **代码侵入性** | 需要读取环境变量 | 无 |

## 推荐选择

### 推荐方案1，如果：
- 你的服务数据库配置相对统一
- 你希望最小化配置管理的复杂度
- 你的团队愿意在服务代码中支持环境变量读取
- 你需要频繁切换环境进行测试

### 推荐方案2，如果：
- 你的服务配置差异较大，需要个性化配置
- 你不想修改现有的服务代码
- 你的配置文件结构比较复杂
- 你的团队习惯于传统的配置文件管理方式

## 注意事项

1. **原始文件保护**：两个方案都在独立目录中，不会影响原始文件
2. **安全性**：生产环境配置包含敏感信息，请妥善保管
3. **版本控制**：建议将配置文件纳入版本控制，注意敏感信息处理
4. **测试验证**：在生产环境使用前，请充分测试配置的正确性

## 技术支持

每个方案目录中都有详细的README文档，包含：
- 详细的使用说明
- 配置示例
- 常见问题解答
- 迁移步骤

如有问题，请查看对应方案的README文档。
