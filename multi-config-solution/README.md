# 方案2：多配置文件方案

## 概述

这个方案为每个服务创建多个环境配置文件（如 config-dev.yaml、config-prod.yaml），通过环境变量动态选择加载哪个配置文件。

## 优势

- ✅ **服务独立配置**：每个服务都有独立的环境配置文件
- ✅ **配置结构清晰**：配置文件按服务和环境分类，结构清晰
- ✅ **个性化配置**：支持服务级别的个性化配置
- ✅ **无需代码修改**：不需要修改服务代码，只需要配置文件
- ✅ **环境隔离**：不同环境的配置完全隔离
- ✅ **易于维护**：配置文件命名规范，易于查找和维护

## 文件结构

```
multi-config-solution/
├── docker-compose.yml         # 修改后的Docker Compose文件
├── start.sh                   # 启动脚本
├── setup-configs.sh           # 配置文件创建脚本
├── projects/                  # 服务配置文件目录
│   ├── iam/
│   │   ├── config.yaml        # 原始配置文件
│   │   ├── config-dev.yaml    # 开发环境配置
│   │   ├── config-test.yaml   # 测试环境配置
│   │   └── config-prod.yaml   # 生产环境配置
│   ├── anno/
│   │   ├── config.yaml
│   │   ├── config-dev.yaml
│   │   ├── config-test.yaml
│   │   └── config-prod.yaml
│   └── ...                    # 其他服务
└── README.md                  # 说明文档
```

## 使用方法

### 1. 初始化配置文件

首次使用时，运行配置文件创建脚本：

```bash
./setup-configs.sh
```

这会为所有服务创建多环境配置文件。

### 2. 启动服务

```bash
# 启动开发环境
./start.sh

# 启动生产环境
./start.sh prod

# 启动测试环境
./start.sh test
```

### 3. 修改配置

根据需要编辑对应环境的配置文件：

```bash
# 修改IAM服务的生产环境配置
vim projects/iam/config-prod.yaml

# 修改Anno服务的开发环境配置
vim projects/anno/config-dev.yaml
```

## 配置文件命名规范

- `config.yaml`: 原始配置文件（保持不变）
- `config-dev.yaml`: 开发环境配置
- `config-test.yaml`: 测试环境配置
- `config-prod.yaml`: 生产环境配置

## 配置示例

### 开发环境配置 (config-dev.yaml)
```yaml
data:
  database:
    driver: postgres
    endpoint: postgres
    port: 5432
    database: iam_v2
    username: root
    password: root
    options: sslmode=disable
  redis:
    addr: redis:6379
```

### 生产环境配置 (config-prod.yaml)
```yaml
data:
  database:
    driver: postgres
    endpoint: prod-postgres.example.com
    port: 5432
    database: iam_prod
    username: prod_user
    password: prod_password_here
    options: sslmode=require
  redis:
    addr: prod-redis.example.com:6379
    password: prod_redis_password
```

## 工作原理

1. **环境变量控制**：通过 `ENVIRONMENT` 环境变量指定当前环境
2. **动态文件映射**：Docker Compose 使用 `${ENVIRONMENT}` 变量动态选择配置文件
3. **文件挂载**：将对应环境的配置文件挂载到容器内的标准路径

## 注意事项

1. **配置文件同步**：确保所有环境的配置文件结构一致
2. **敏感信息**：生产环境配置文件包含敏感信息，请妥善保管
3. **版本控制**：建议将配置文件纳入版本控制，但要注意敏感信息的处理
4. **配置验证**：启动前会自动检查配置文件是否存在

## 迁移步骤

1. 运行 `./setup-configs.sh` 创建多环境配置文件
2. 根据实际需求修改各环境的配置文件
3. 使用 `./start.sh [env]` 启动对应环境的服务
4. 测试各环境的服务是否正常运行

## 与方案1的对比

| 特性 | 方案1（环境变量） | 方案2（多配置文件） |
|------|------------------|-------------------|
| 配置集中度 | 高（一个文件） | 低（多个文件） |
| 服务个性化 | 需要代码支持 | 天然支持 |
| 配置复杂度 | 简单 | 中等 |
| 维护成本 | 低 | 中等 |
| 灵活性 | 中等 | 高 |
