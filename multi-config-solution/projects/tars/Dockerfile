# build program
ARG IMAGE_PREFIX
FROM ${IMAGE_PREFIX}golang:1.22-alpine AS builder

ARG TEST
ARG VERSION
ARG GOPROXY
ARG GOPRIVATE
ENV LDFLAGS="-s"

COPY . /src
WORKDIR /src

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add --no-cache make git
RUN if [ -n "$TEST" ]; then make migrations; fi
RUN make build
RUN if [ -n "$TEST" ]; then apk add --no-cache build-base; make test; fi

RUN mkdir -p /app/migrations
COPY anyconn/internal/data/migrations /app/migrations/anyconn
COPY model/internal/data/migrations /app/migrations/model

RUN mkdir -p /data/conf
#COPY anyconn/configs/config-local.yaml /data/conf/anyconn.yaml
#COPY model/configs/config.yaml /data/conf/model.yaml
#COPY tars/configs/config-local.yaml /data/conf/tars.yaml

RUN mkdir -p /www
COPY tars/internal/server/www /www/tars


# create image
FROM ${IMAGE_PREFIX}alpine:3.18

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update && apk add --no-cache ca-certificates tzdata && update-ca-certificates

RUN mkdir -p /app/migrations
COPY --from=builder /src/bin /app
COPY --from=builder /app/migrations /app/migrations

RUN mkdir -p /data/conf
COPY --from=builder /data/conf /data/conf

RUN mkdir -p /www
COPY --from=builder /www /www

WORKDIR /app

EXPOSE 6060
EXPOSE 8050 8051
EXPOSE 8060 8061
EXPOSE 8070 8071
VOLUME /data/conf
VOLUME /work

CMD ["/app/unicorn", "tars", "serve", "--conf", "/data/conf/tars.yaml"]
