server:
  http:
    addr: 0.0.0.0:8020
    timeout: 10s
  grpc:
    addr: 0.0.0.0:8021
    timeout: 60s
  # non-empty comma-separated list of origins will enable CORS
  #cors_origins: "*"
data:
  database:
    driver: postgres
    #source: postgres://root:root@localhost:5432/annofeed?sslmode=disable
    endpoint: postgres
    port: 5432
    database: annofeed_v2
    username: root
    password: root
    options: sslmode=disable
    max_idle_conns: 5
    max_open_conns: 20
    conn_max_idle_time: 600s
  redis:
    addr: redis:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

workspace:

file_server:
  storage: s3 #s3

  # local storage config
  # directory in local filesystem to put the uploaded files
  base_dir: upload

  # s3 storage config
  # store normal data
  bucket: non-prod-workload-sansheng
  # store public access resources: user/project/label avatars
  public_bucket: konvery-images-public-nonprod
  # base URL for the uploaded avatars
  avatar_url: https://s3npip.d.konvery.com

  # if allow anonymous to access rawdatas
  public_rawdata: true
  # max is 7 days, set to 3 days
  presign_expires: 518400s
  # service URL
#  svc_url: http://127.0.0.1:8020/v1
  svc_url: https://local.konvery.work/annofeed/v1

  s3:
    access_key: ********************
    secret_key: Q95HadgHsDPsXaRc4z3n0H2NfrS9GGFO5yUQfCMq

  cloudfront:
    enabled: false
    # signer key ID
    sign_key_id: KJBD2UMX2E3GW
    # signer private key (RSA) in PEM format
    sign_key: | 
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    # default expires duration for signed URLs: 3 days
    expires: 259200s

    distributions:
      dist1:
        # matching origin, including the s3 scheme, bucket name and the path if any: s3://bucket/path
        origin: s3://konvery-images-public-nonprod
        # access URL prefix, including scheme and domain name: https://example.com
        url_prefix: https://s3npip.d.konvery.com
        # if it is a public distribution
        public: true
      dist2:
        origin: s3://non-prod-workload-sansheng
        url_prefix: https://s3npss.d.konvery.com

temporal:
  disable_worker: false
  addr: temporal:7233
  namespace:
  task_queue: annofeed
  worker_storage_gb: 100

ktwf:
  master:
    #image: artifactory.rp.konvery.work/docker/sansheng-annofeed:latest
    #command: ["/app/annofeed", "-conf", "/data/conf"]
    args: []
    #service_account: sansheng-annofeed
    storage_class: ebs-kvy
  worker:
    namespace: sansheng-sandbox
    service_account: default

rpc:
  # service account used for RPC calls
  svc_account: aaaaaaaanno

  iam:
    addr: iam:8001
  anno:
    addr: anno:8011

otel:
  tracing:
    endpoint: #127.0.0.1:4317
  metrics:
    serve_addr: #:6060
  log:
    level: debug # info
    format: default

# CAUTION: Never configure these in production!
danger_test:

mq:
  producer:
    provider: unspecified
    topic: arn:aws-cn:sns:cn-northwest-1:************:non-prod-workload-sns-sansheng-annofeed
  consumer:
    provider: redis_pubsub
#    handler_threads: 1
    # comma seperated topics list (not for AWS SQS consumers)
    topics: "test-anno-mq"
  sqs:
    name: non-prod-workload-sqs-sansheng-annofeed
