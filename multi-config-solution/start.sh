#!/bin/bash

# 方案2：多配置文件方案启动脚本
# 使用方法：
# ./start.sh                    # 使用默认开发环境(config-dev.yaml)
# ./start.sh prod               # 使用生产环境(config-prod.yaml)
# ./start.sh test               # 使用测试环境(config-test.yaml)

set -e

# 默认环境为dev
ENVIRONMENT=${1:-dev}

# 支持的环境列表
SUPPORTED_ENVS=("dev" "test" "prod")

# 检查环境是否支持
if [[ ! " ${SUPPORTED_ENVS[@]} " =~ " ${ENVIRONMENT} " ]]; then
    echo "❌ 不支持的环境: $ENVIRONMENT"
    echo "支持的环境: ${SUPPORTED_ENVS[*]}"
    exit 1
fi

echo "🚀 启动环境: $ENVIRONMENT"

# 检查配置文件是否存在
echo "🔍 检查配置文件..."
missing_configs=()

# 服务列表
SERVICES=("iam" "anno" "annofeed" "annout" "annostat" "anycorn" "tars")

for service in "${SERVICES[@]}"; do
    config_file="projects/$service/config-$ENVIRONMENT.yaml"
    if [ ! -f "$config_file" ]; then
        missing_configs+=("$config_file")
    fi
done

# 如果有缺失的配置文件，提示用户
if [ ${#missing_configs[@]} -ne 0 ]; then
    echo "⚠️  以下配置文件不存在："
    for config in "${missing_configs[@]}"; do
        echo "   - $config"
    done
    echo ""
    echo "💡 运行以下命令创建配置文件："
    echo "   ./setup-configs.sh"
    echo ""
    read -p "是否继续启动？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 启动已取消"
        exit 1
    fi
fi

# 设置环境变量并启动服务
echo "🔄 启动Docker Compose服务..."
export ENVIRONMENT=$ENVIRONMENT
docker-compose up -d

echo "✅ 服务启动完成！"
echo "📊 当前环境: $ENVIRONMENT"
echo "📝 查看服务状态: docker-compose ps"
echo "📝 查看日志: docker-compose logs -f [service_name]"
echo ""
echo "💡 优势："
echo "   - 每个服务独立的环境配置文件"
echo "   - 配置文件结构清晰，易于管理"
echo "   - 支持服务级别的个性化配置"
echo "   - 不需要修改服务代码"
