# 方案1：环境变量配置方案

## 概述

这个方案通过环境变量统一管理所有服务的数据库配置，解决了原来需要修改多个配置文件的问题。

## 优势

- ✅ **统一管理**：所有数据库配置集中在一个文件中
- ✅ **DSN支持**：支持DSN格式连接字符串，更加灵活
- ✅ **多环境支持**：轻松切换开发、测试、生产环境
- ✅ **一次修改，全局生效**：修改一个配置文件，所有服务自动生效
- ✅ **向后兼容**：不需要修改服务代码，只需要在配置文件中读取环境变量

## 文件结构

```
env-config-solution/
├── .env                    # 开发环境配置
├── .env.prod              # 生产环境配置
├── .env.test              # 测试环境配置（可选）
├── docker-compose.yml     # 修改后的Docker Compose文件
├── start.sh               # 启动脚本
├── projects/              # 服务配置文件目录
└── README.md              # 说明文档
```

## 使用方法

### 1. 启动服务

```bash
# 启动开发环境
./start.sh

# 启动生产环境
./start.sh prod

# 启动测试环境
./start.sh test
```

### 2. 修改数据库配置

只需要编辑对应环境的配置文件：

- 开发环境：编辑 `.env`
- 生产环境：编辑 `.env.prod`
- 测试环境：编辑 `.env.test`

### 3. 配置示例

#### 开发环境 (.env)
```bash
# 数据库配置
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=root
POSTGRES_PASSWORD=root

# DSN格式（推荐）
IAM_DATABASE_DSN=**********************************/iam_v2?sslmode=disable
```

#### 生产环境 (.env.prod)
```bash
# 数据库配置
POSTGRES_HOST=prod-postgres.example.com
POSTGRES_PORT=5432
POSTGRES_USER=prod_user
POSTGRES_PASSWORD=prod_password_here

# DSN格式（推荐）
IAM_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/iam_prod?sslmode=require
```

## 配置说明

### 数据库配置
- `POSTGRES_HOST`: 数据库主机地址
- `POSTGRES_PORT`: 数据库端口
- `POSTGRES_USER`: 数据库用户名
- `POSTGRES_PASSWORD`: 数据库密码
- `POSTGRES_OPTIONS`: 数据库连接选项

### DSN配置（推荐）
- `IAM_DATABASE_DSN`: IAM服务数据库DSN
- `ANNO_DATABASE_DSN`: Anno服务数据库DSN
- `ANNOFEED_DATABASE_DSN`: Annofeed服务数据库DSN
- 等等...

### Redis配置
- `REDIS_ADDR`: Redis地址，格式：host:port

## 注意事项

1. **网络依赖**：需要先启动基础服务创建网络，或确保 `local-dev_dev-network` 网络存在
2. **Kratos框架**：环境变量命名遵循 `DATA_DATABASE_XXX` 格式，详见 `KRATOS_CONFIG.md`
3. **安全性**：生产环境的配置文件包含敏感信息，请妥善保管
4. **环境变量优先级**：环境变量会覆盖配置文件中的对应设置

## 迁移步骤

1. **确保基础服务运行**：
   ```bash
   cd .. && docker-compose -f depends.yml up -d
   ```

2. **配置环境变量**：
   - 编辑 `.env` 文件，设置数据库连接信息
   - 参考 `KRATOS_CONFIG.md` 了解Kratos框架的环境变量规则

3. **启动服务**：
   ```bash
   ./start.sh          # 开发环境
   ./start.sh prod      # 生产环境
   ```

4. **验证配置**：
   ```bash
   docker-compose logs iam    # 查看服务日志
   docker exec -it iam env | grep DATA_DATABASE  # 检查环境变量
   ```
