# 方案1：环境变量配置方案

## 概述

这个方案通过环境变量统一管理所有服务的数据库配置，解决了原来需要修改多个配置文件的问题。

## 优势

- ✅ **统一管理**：所有数据库配置集中在一个文件中
- ✅ **DSN支持**：支持DSN格式连接字符串，更加灵活
- ✅ **多环境支持**：轻松切换开发、测试、生产环境
- ✅ **一次修改，全局生效**：修改一个配置文件，所有服务自动生效
- ✅ **向后兼容**：不需要修改服务代码，只需要在配置文件中读取环境变量

## 文件结构

```
env-config-solution/
├── .env                    # 开发环境配置
├── .env.prod              # 生产环境配置
├── .env.test              # 测试环境配置（可选）
├── docker-compose.yml     # 修改后的Docker Compose文件
├── start.sh               # 启动脚本
├── projects/              # 服务配置文件目录
└── README.md              # 说明文档
```

## 使用方法

### 1. 启动服务

```bash
# 启动开发环境
./start.sh

# 启动生产环境
./start.sh prod

# 启动测试环境
./start.sh test
```

### 2. 修改数据库配置

只需要编辑对应环境的配置文件：

- 开发环境：编辑 `.env`
- 生产环境：编辑 `.env.prod`
- 测试环境：编辑 `.env.test`

### 3. 配置示例

#### 开发环境 (.env)
```bash
# 数据库配置
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=root
POSTGRES_PASSWORD=root

# DSN格式（推荐）
IAM_DATABASE_DSN=**********************************/iam_v2?sslmode=disable
```

#### 生产环境 (.env.prod)
```bash
# 数据库配置
POSTGRES_HOST=prod-postgres.example.com
POSTGRES_PORT=5432
POSTGRES_USER=prod_user
POSTGRES_PASSWORD=prod_password_here

# DSN格式（推荐）
IAM_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/iam_prod?sslmode=require
```

## 配置说明

### 数据库配置
- `POSTGRES_HOST`: 数据库主机地址
- `POSTGRES_PORT`: 数据库端口
- `POSTGRES_USER`: 数据库用户名
- `POSTGRES_PASSWORD`: 数据库密码
- `POSTGRES_OPTIONS`: 数据库连接选项

### DSN配置（推荐）
- `IAM_DATABASE_DSN`: IAM服务数据库DSN
- `ANNO_DATABASE_DSN`: Anno服务数据库DSN
- `ANNOFEED_DATABASE_DSN`: Annofeed服务数据库DSN
- 等等...

### Redis配置
- `REDIS_ADDR`: Redis地址，格式：host:port

## 注意事项

1. **安全性**：生产环境的配置文件包含敏感信息，请妥善保管
2. **环境变量优先级**：Docker Compose中的环境变量会覆盖配置文件中的值
3. **服务代码修改**：需要确保服务代码能够读取环境变量中的配置

## 迁移步骤

1. 复制现有的配置文件到此目录
2. 修改各服务的配置文件，使其读取环境变量
3. 根据需要调整 `.env` 文件中的配置
4. 使用 `./start.sh` 启动服务进行测试
