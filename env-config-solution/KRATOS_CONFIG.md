# Kratos框架环境变量配置说明

## 概述

Kratos框架支持通过环境变量覆盖配置文件中的设置。环境变量的命名规则是将配置文件中的嵌套路径用下划线连接，并转换为大写。

## 配置映射关系

### 数据库配置

| 配置文件路径 | 环境变量名 | 说明 |
|-------------|-----------|------|
| `data.database.driver` | `DATA_DATABASE_DRIVER` | 数据库驱动类型 |
| `data.database.endpoint` | `DATA_DATABASE_ENDPOINT` | 数据库主机地址 |
| `data.database.port` | `DATA_DATABASE_PORT` | 数据库端口 |
| `data.database.database` | `DATA_DATABASE_DATABASE` | 数据库名称 |
| `data.database.username` | `DATA_DATABASE_USERNAME` | 数据库用户名 |
| `data.database.password` | `DATA_DATABASE_PASSWORD` | 数据库密码 |
| `data.database.options` | `DATA_DATABASE_OPTIONS` | 数据库连接选项 |
| `data.database.max_idle_conns` | `DATA_DATABASE_MAX_IDLE_CONNS` | 最大空闲连接数 |
| `data.database.max_open_conns` | `DATA_DATABASE_MAX_OPEN_CONNS` | 最大打开连接数 |
| `data.database.conn_max_idle_time` | `DATA_DATABASE_CONN_MAX_IDLE_TIME` | 连接最大空闲时间 |

### Redis配置

| 配置文件路径 | 环境变量名 | 说明 |
|-------------|-----------|------|
| `data.redis.addr` | `DATA_REDIS_ADDR` | Redis地址 |
| `data.redis.password` | `DATA_REDIS_PASSWORD` | Redis密码 |
| `data.redis.read_timeout` | `DATA_REDIS_READ_TIMEOUT` | 读取超时时间 |
| `data.redis.write_timeout` | `DATA_REDIS_WRITE_TIMEOUT` | 写入超时时间 |

## 配置示例

### 原始config.yaml
```yaml
data:
  database:
    driver: postgres
    endpoint: postgres
    port: 5432
    database: iam_v2
    username: root
    password: root
    options: sslmode=disable
    max_idle_conns: 5
    max_open_conns: 20
    conn_max_idle_time: 600s
  redis:
    addr: redis:6379
    read_timeout: 0.2s
    write_timeout: 0.2s
```

### 对应的环境变量
```bash
DATA_DATABASE_DRIVER=postgres
DATA_DATABASE_ENDPOINT=postgres
DATA_DATABASE_PORT=5432
DATA_DATABASE_DATABASE=iam
DATA_DATABASE_USERNAME=root
DATA_DATABASE_PASSWORD=root
DATA_DATABASE_OPTIONS=sslmode=disable
DATA_DATABASE_MAX_IDLE_CONNS=5
DATA_DATABASE_MAX_OPEN_CONNS=20
DATA_DATABASE_CONN_MAX_IDLE_TIME=600s
DATA_REDIS_ADDR=redis:6379
DATA_REDIS_READ_TIMEOUT=0.2s
DATA_REDIS_WRITE_TIMEOUT=0.2s
```

## 特殊服务配置

### Keto服务
Keto服务使用直接的DSN配置，通过`DSN`环境变量设置：

```bash
DSN=**********************************/keto_v2?sslmode=disable&max_conns=20&max_idle_conns=4&max_conn_lifetime=10h&max_conn_idle_time=1h
```

## 验证配置

启动服务后，可以通过以下方式验证环境变量是否生效：

1. **查看容器环境变量**：
```bash
docker exec -it iam env | grep DATA_DATABASE
```

2. **查看服务日志**：
```bash
docker-compose logs iam
```

3. **测试数据库连接**：
```bash
docker exec -it iam sh -c "nc -zv postgres 5432"
```

## 注意事项

1. **环境变量优先级**：环境变量会覆盖配置文件中的对应设置
2. **数据类型**：确保环境变量的值类型与配置文件中的期望类型一致
3. **服务重启**：修改环境变量后需要重启服务才能生效
4. **调试模式**：可以通过日志查看实际使用的配置值

## 常见问题

### Q: 环境变量不生效？
A: 检查以下几点：
- 环境变量名称是否正确（大小写敏感）
- 是否重启了服务
- 查看服务日志确认配置加载情况

### Q: 数据库连接失败？
A: 检查：
- 数据库服务是否启动
- 网络连接是否正常
- 数据库名称、用户名、密码是否正确
