# version: '3'
services:
  # postgres:
  #   image: postgres:latest
  #   container_name: postgres
  #   restart: always
  #   ports:
  #     - 5432:5432
  #   networks:
  #     - dev-network
  #   environment:
  #     POSTGRES_USER: root
  #     POSTGRES_PASSWORD: root
  #     POSTGRES_DB: anyconn
  #   volumes:
  #     - ./postgres/data:/var/lib/postgresql/data
      
  # redis:
  #   image: redis:latest
  #   container_name: redis
  #   restart: always
  #   ports:
  #     - 6379:6379
  #   networks:
  #     - dev-network
  #   privileged: true
  #   environment:
  #     - "TZ=Asia/Shanghai"
  #   volumes:
  #     - ./redis/data:/data
  #   command: redis-server --appendonly yes
    # command: redis-server --requirepass 123456 --appendonly yes

  keto:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/keto/
      dockerfile: /Users/<USER>/docker/local-dev/projects/keto/Dockerfile
      # dockerfile: Dockerfile.local
    container_name: keto
    ports:
      - 4466:4466
      - 4467:4467
    volumes:
      - ./projects/keto/keto.yml:/home/<USER>/keto.yml
      # - ./projects/keto/namespaces.keto.ts:/home/<USER>/namespaces.keto.ts
      # - ./projects/keto/tuples.txt:/home/<USER>/tuples.txt
    environment:
      - "LOG_LEVEL=debug"
      - "DUMP_CACHE=false"
      - "SHOW_DB_QUERY=true"
      - "SHOW_OBJ_NAME=true"
      - "SQA_OPT_OUT=true"
      - "NAMESPACES_EXPERIMENTAL_STRICT_MODE=true"
      - "DBG_CACHE_REFRESH_SECONDS=600"
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis


  iam:
    build:
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/iam/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/iam/Dockerfile
      # dockerfile: Dockerfile.local
    container_name: iam
    ports:
      - 8000:8000
      - 8001:8001
    volumes:
      - ./projects/iam/config.yaml:/data/conf/config.yaml
    environment:
      # 数据库配置环境变量
      - POSTGRES_HOST=${POSTGRES_HOST:-postgres}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_USER=${POSTGRES_USER:-root}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-root}
      - POSTGRES_OPTIONS=${POSTGRES_OPTIONS:-sslmode=disable}
      - IAM_DB_NAME=${IAM_DB_NAME:-iam_v2}
      - IAM_DATABASE_DSN=${IAM_DATABASE_DSN:-**********************************/iam_v2?sslmode=disable}
      # Redis配置环境变量
      - REDIS_ADDR=${REDIS_ADDR:-redis:6379}
      # 其他公共配置
      - TEMPORAL_ADDR=${TEMPORAL_ADDR:-temporal:7233}
      - KETO_READ_ADDR=${KETO_READ_ADDR:-keto:4466}
      - KETO_WRITE_ADDR=${KETO_WRITE_ADDR:-keto:4467}
      - ENVIRONMENT=${ENVIRONMENT:-dev}
    env_file:
      - .env
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis

  anno:
    build:
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/anno/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/anno/Dockerfile
      # dockerfile: Dockerfile.local
    container_name: anno
    ports:
      - 8010:8010
      - 8011:8011
    volumes:
      - ./projects/anno/config.yaml:/data/conf/config.yaml
    networks:
      - dev-network
    environment:
      # 数据库配置环境变量
      - POSTGRES_HOST=${POSTGRES_HOST:-postgres}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_USER=${POSTGRES_USER:-root}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-root}
      - POSTGRES_OPTIONS=${POSTGRES_OPTIONS:-sslmode=disable}
      - ANNO_DB_NAME=${ANNO_DB_NAME:-anno_v2}
      - ANNO_DATABASE_DSN=${ANNO_DATABASE_DSN:-**********************************/anno_v2?sslmode=disable}
      # Redis配置环境变量
      - REDIS_ADDR=${REDIS_ADDR:-redis:6379}
      # 其他公共配置
      - TEMPORAL_ADDR=${TEMPORAL_ADDR:-temporal:7233}
      - ENVIRONMENT=${ENVIRONMENT:-dev}
      # AWS配置
      # - "DBG_SAVE_BIG_VALUES_IN_DB=true"
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
      - "LOT_ENABLE_JOB_CAM_PARAMS=true"
    env_file:
      - .env
    depends_on:
      - iam
      - annofeed
      - annout
      - annostat

  annofeed:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/annofeed/
      dockerfile: /Users/<USER>/docker/local-dev/projects/annofeed/Dockerfile
      # dockerfile: Dockerfile.local
    container_name: annofeed
    ports:
      - 8020:8020
      - 8021:8021
    networks:
      - dev-network
    volumes:
      - ./projects/annofeed/config.yaml:/data/conf/config.yaml
      - ./projects/annofeed/token:/var/run/secrets/eks.amazonaws.com/serviceaccount/token
      - /var/run/docker.sock:/var/run/docker.sock
      # - /Users/<USER>/.docker/config.json:/root/.docker/config.json
      - /tmp:/tmp
    environment:
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
      # - TESTCONTAINERS_RYUK_CONTAINER_PRIVILEGED=true
      # - TESTCONTAINERS_RYUK_DISABLED=true
      # - DOCKER_HOST=tcp://host.docker.internal:2375
      - TESTCONTAINERS_HOST_OVERRIDE=host.docker.internal
    # depends_on:
    #   - postgres
    #   - redis

  annout:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/annout/
      dockerfile: /Users/<USER>/docker/local-dev/projects/annout/Dockerfile
      # dockerfile: Dockerfile.local
    container_name: annout
    ports:
      - 8030:8030
      - 8031:8031
    environment:
      # - "DBG_SAVE_BIG_VALUES_IN_DB=true"
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
    volumes:
      - ./projects/annout/config.yaml:/data/conf/config.yaml
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis

  annostat:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/annostat/
      dockerfile: /Users/<USER>/docker/local-dev/projects/annostat/Dockerfile
      # dockerfile: Dockerfile.local
    container_name: annostat
    ports:
      - 8040:8040
      - 8041:8041
    volumes:
      - ./projects/annostat/config.yaml:/data/conf/config.yaml
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis

  anycorn:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/unicorn/
      dockerfile: /Users/<USER>/docker/local-dev/projects/anycorn/Dockerfile
      # dockerfile: Dockerfile.anycorn
    container_name: anycorn
    ports:
      - 8050:8050
      - 8051:8051
    volumes:
      - ./projects/anycorn/config.yaml:/data/conf/anyconn.yaml
    environment:
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis

  tars:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/unicorn/
      dockerfile: /Users/<USER>/docker/local-dev/projects/tars/Dockerfile
      # dockerfile: Dockerfile.tars
    container_name: tars
    ports:
      - 8070:8070
      - 8071:8071
    volumes:
      - ./projects/tars/config.yaml:/data/conf/tars.yaml
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis

  # mysql:
  #   image: mysql:laster
  #   restart: always
  #   ports:
  #     - 13306:3306
  #   networks:
  #     - front-ms
  #   privileged: true
  #   container_name: mysql
  #   environment:
  #     - "MYSQL_ROOT_PASSWORD=aike@2022"
  #     - "MYSQL_DATABASE=test"
  #     - "TZ=Asia/Shanghai"
  #   command:
  #     --default-time-zone='+8:00'
  #     --default-authentication-plugin=mysql_native_password
  #     --character-set-server=utf8mb4
  #     --collation-server=utf8mb4_general_ci
  #     --max_connections=1000
  #     --innodb_lock_wait_timeout=500
  #   volumes:
  #     - /etc/localtime:/etc/localtime:ro
  #     - /home/<USER>/mysql/data:/var/lib/mysql/
  #     - /home/<USER>/mysql/logs:/var/log/mysql
  #     - /home/<USER>/mysql/conf/conf.d:/etc/mysql/conf.d/
  #     - /home/<USER>/mysql/conf/my.cnf:/etc/mysql/my.cnf

  nginx:
    image: nginx:latest
    container_name: nginx
    privileged: true
    restart: always
    ports:
      - 80:80
      - 443:443
    environment:
      - "TZ=Asia/Shanghai"
    volumes:
      - ./nginx/logs:/var/log/nginx/
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf/default.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/www/:/usr/share/nginx/html/
      - ./nginx/ssl:/etc/nginx/ssl/
    command: /bin/bash -c "nginx -g 'daemon off;'"
    networks:
      - dev-network
    depends_on:
      - iam
      - keto
      - anno
      - annofeed
      - annout
      - annostat

networks:
  dev-network:
    driver: bridge
