# 生产环境配置
# 数据库配置
POSTGRES_HOST=prod-postgres.example.com
POSTGRES_PORT=5432
POSTGRES_USER=prod_user
POSTGRES_PASSWORD=prod_password_here
POSTGRES_OPTIONS=sslmode=require

# Redis配置
REDIS_HOST=prod-redis.example.com
REDIS_PORT=6379
REDIS_PASSWORD=prod_redis_password

# 各服务数据库名
IAM_DB_NAME=iam_prod
ANNO_DB_NAME=anno_prod
ANNOFEED_DB_NAME=annofeed_prod
ANNOUT_DB_NAME=annout_prod
ANNOSTAT_DB_NAME=annostat_prod
ANYCORN_DB_NAME=anycorn_prod
TARS_DB_NAME=tars_prod

# DSN格式连接字符串（推荐使用）
IAM_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/iam_prod?sslmode=require
ANNO_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/anno_prod?sslmode=require
ANNOFEED_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/annofeed_prod?sslmode=require
ANNOUT_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/annout_prod?sslmode=require
ANNOSTAT_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/annostat_prod?sslmode=require
ANYCORN_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/anycorn_prod?sslmode=require
TARS_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/tars_prod?sslmode=require

# Redis连接字符串
REDIS_ADDR=prod-redis.example.com:6379

# 其他公共配置
TEMPORAL_ADDR=prod-temporal.example.com:7233
KETO_READ_ADDR=prod-keto.example.com:4466
KETO_WRITE_ADDR=prod-keto.example.com:4467

# 环境标识
ENVIRONMENT=prod

# MinIO配置（生产环境）
minio_ak=PROD_MINIO_ACCESS_KEY
minio_sk=PROD_MINIO_SECRET_KEY
minio_endpoint=https://prod-minio.example.com
minio-bucket=prod-minio-bucket
