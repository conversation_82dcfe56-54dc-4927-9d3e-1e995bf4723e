apisix:
  node_listen: 
   - 9080              # APISIX listening port
   - 80
   # - 443
  # proxy_protocol:                    # PROXY Protocol configuration
    # listen_http_port: 80           # APISIX listening port for HTTP traffic with PROXY protocol.
    # listen_https_port: 443          # APISIX listening port for HTTPS traffic with PROXY protocol.
    # enable_tcp_pp: true              # Enable the PROXY protocol when stream_proxy.tcp is set.
    # enable_tcp_pp_to_upstream: true  # Enable the PROXY protocol.
  
  enable_ipv6: false
  enable_http2: false

  enable_control: true
  control:
    ip: "0.0.0.0"
    port: 9092

  ssl:
    enable: true
    listen:
      - port: 443

deployment:
  admin:
    allow_admin:               # https://nginx.org/en/docs/http/ngx_http_access_module.html#allow
      - 0.0.0.0/0              # We need to restrict ip access rules for security. 0.0.0.0/0 is for test.

    admin_key:
      - name: "admin"
        key: admin
        role: admin                 # admin: manage all configuration data

      - name: "viewer"
        key: view
        role: viewer

  etcd:
    host:                           # it's possible to define multiple etcd hosts addresses of the same etcd cluster.
      - "http://etcd:2379"          # multiple etcd address
    prefix: "/apisix"               # apisix configurations prefix
    timeout: 30                     # 30 seconds


# plugin_attr:
#   prometheus:
#     export_addr:
#       ip: "0.0.0.0"
#       port: 9091

# plugins:
#   - error-log-logger