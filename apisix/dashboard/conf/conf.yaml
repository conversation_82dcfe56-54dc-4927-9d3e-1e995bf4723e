conf:
  listen:
    host: 0.0.0.0         # `manager api` listening ip or host name
    port: 9000            # `manager api` listening port

  etcd:
    prefix: "/apisix"
    endpoints:            # supports defining multiple etcd host addresses for an etcd cluster
      - http://etcd:2379

                          # etcd basic auth info
    # username: "root"    # ignore etcd username if not enable etcd auth
    # password: "123456"  # ignore etcd password if not enable etcd auth
  log:
    error_log:
      level: warn         # supports levels, lower to higher: debug, info, warn, error, panic, fatal
      file_path:
        /dev/stderr
        # logs/error.log    # supports relative path, absolute path, standard output
                          # such as: logs/error.log, /tmp/logs/error.log, /dev/stdout, /dev/stderr
    access_log:
      file_path:
        /dev/stdout

authentication:
  secret:
    secret                # secret for jwt token generation.
                          # NOTE: Highly recommended to modify this value to protect `manager api`.
                          # if it's default value, when `manager api` start, it will generate a random string to replace it.
  expire_time: 3600       # jwt token expire time, in second
  users:
    - username: admin     # username and password for login `manager api`
      password: admin

    - username: user
      password: user

# plugin_attr:
#   prometheus:
#     export_addr:
#       ip: "0.0.0.0"
#       port: 9091
